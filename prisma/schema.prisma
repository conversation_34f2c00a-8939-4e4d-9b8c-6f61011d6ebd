datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User{
    account           String @id @unique
    password          String
    name              String   
    phone             String?
    email             String?
    role              Role   @default(USER)
    managedProjects   ProjectManager[]
    permissions       Permission[]
    members           Member[]
    @@map("User")
}

model Member {
  id        Int     @id @default(autoincrement())
  account   String
  gid       Int

  user      User @relation(fields: [account],references: [account], onDelete: Cascade)
  group     MemberGroup @relation(fields: [gid], references: [id], onDelete: Cascade)

  @@map("Member")
}

model MemberGroup{
    id          Int     @id @default(autoincrement())
    name        String
    description String?
    project_id  Int
    invite_code String @unique
    permissions Permission[]
    members     Member[]
    @@map("MemberGroup")
}

model Project{
    id          Int     @id @default(autoincrement())
    name        String    
    description String
    status      ProjectStatus @default(CLOSE)
    start_time  String?
    end_time    String?
    expiration  DateTime?
    hashID      String?
    salt        String?
    created_by  String
    created_at  DateTime @default(now())
    managers    ProjectManager[]
    permissions Permission[]
    @@map("Project")
}

model ProjectManager{
    account    String
    id         Int

    project    Project           @relation(fields: [id], references: [id],onDelete: Cascade)
    manager    User              @relation(fields: [account], references: [account])
    @@id([account,id])
    @@map("ProjectManager")
}

model Exhibition{
  id          Int @id @default(autoincrement())
  pid         Int 
  name        String
  description String
  status      ProjectStatus @default(CLOSE)
  start_time  String?
  end_time    String?
  expiration  DateTime?
  hashID      String?
  salt        String?
  created_by  String
  game        Game @default(LuckyDart)
  created_at  DateTime @default(now())

  videos Video[]
}

model Permission{
    id           Int     @id @default(autoincrement())
    pid          Int
    group_id     Int?
    user_id      String?
    permission   Permissions @default(ALL)

    project      Project @relation(fields: [pid], references: [id], onDelete: Cascade)
    group        MemberGroup? @relation(fields: [group_id], references: [id], onDelete: Cascade)
    user         User? @relation(fields: [user_id], references: [account], onDelete: Cascade)
    @@map("Permission")
}

model Video {
  id            Int     @id @default(autoincrement())
  name          String
  description   String?  @db.Text
  eid           Int
  url           String
  status        VideoStatus  @default(PENDING)
  sourceType    VideoSource @default(YOUTUBE)
  uploader      String?
  published_at  DateTime?
  duration      String?
  tags          String? @db.Text

  exhibition       Exhibition @relation(fields: [eid], references: [id], onDelete: Cascade)
  video_stats   VideoPlayStats[]
  @@map("Video")
}

model VideoPlayStats {
  id        Int     @id @default(autoincrement())  // 唯一ID
  videoId   Int                                    // 關聯到影片的ID
  playTime  Float                                  // 播放的時間

  video     Video      @relation(fields: [videoId], references: [id], onDelete: Cascade) // 關聯到video表
  @@map("video_play_stats")                            // 對應SQL中的表名
}

enum Role{
  USER
  ADMIN
  SUPERADMIN
}

enum VideoSource{
  YOUTUBE
  S3
  VIMEO
  LOCAL
}

enum VideoStatus{
  PENDING
  APPROVED
  INACTIVE
  ACTIVE
}

enum ProjectStatus{
  CLOSE
  OPEN
}

enum Game{
  LuckyDart
}
enum Permissions{
  ALL
  VIEW
  EDIT
  DELETE
}