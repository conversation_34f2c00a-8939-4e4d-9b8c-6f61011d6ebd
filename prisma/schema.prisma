datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

model User{
    account           String @id @unique
    password          String
    name              String   
    phone             String?
    email             String?
    role              Role   @default(USER)
    managedProjects   ProjectManager[]
    permissions       Permission[]
    members           Member[]
    @@map("User")
}

model Member {
  id        Int     @id @default(autoincrement())
  account   String
  gid       Int

  user      User @relation(fields: [account],references: [account], onDelete: Cascade)
  group     MemberGroup @relation(fields: [gid], references: [id], onDelete: Cascade)

  @@map("Member")
}

model MemberGroup{
    id          Int     @id @default(autoincrement())
    name        String
    description String?
    project_id  Int
    invite_code String @unique
    permissions Permission[]
    members     Member[]
    @@map("MemberGroup")
}

model Project{
    id          Int     @id @default(autoincrement())
    name        String    
    description String
    status      ProjectStatus @default(CLOSE)
    start_time  String?
    end_time    String?
    expiration  DateTime?
    hashID      String?
    salt        String?
    created_by  String
    created_at  DateTime @default(now())
    managers    ProjectManager[]
    permissions Permission[]
    @@map("Project")
}

model ProjectManager{
    account    String
    id         Int

    project    Project           @relation(fields: [id], references: [id],onDelete: Cascade)
    manager    User              @relation(fields: [account], references: [account])
    @@id([account,id])
    @@map("ProjectManager")
}

model Exhibition{
  id          Int @id @default(autoincrement())
  pid         Int
  name        String
  description String
  status      ProjectStatus @default(CLOSE)
  start_time  String?
  end_time    String?
  expiration  DateTime?
  hashID      String?
  salt        String?
  created_by  String
  game        Game @default(LuckyDart)
  created_at  DateTime @default(now())

  videos Video[]
  registrations EventRegistration[]
  gameRooms   GameRoom[]
}

model Permission{
    id           Int     @id @default(autoincrement())
    pid          Int
    group_id     Int?
    user_id      String?
    permission   Permissions @default(ALL)

    project      Project @relation(fields: [pid], references: [id], onDelete: Cascade)
    group        MemberGroup? @relation(fields: [group_id], references: [id], onDelete: Cascade)
    user         User? @relation(fields: [user_id], references: [account], onDelete: Cascade)
    @@map("Permission")
}

model Video {
  id            Int     @id @default(autoincrement())
  name          String
  description   String?  @db.Text
  eid           Int
  url           String
  status        VideoStatus  @default(PENDING)
  sourceType    VideoSource @default(YOUTUBE)
  uploader      String?
  published_at  DateTime?
  duration      String?
  tags          String? @db.Text

  exhibition       Exhibition @relation(fields: [eid], references: [id], onDelete: Cascade)
  video_stats   VideoPlayStats[]
  @@map("Video")
}

model VideoPlayStats {
  id        Int     @id @default(autoincrement())  // 唯一ID
  videoId   Int                                    // 關聯到影片的ID
  playTime  Float                                  // 播放的時間

  video     Video      @relation(fields: [videoId], references: [id], onDelete: Cascade) // 關聯到video表
  @@map("video_play_stats")                            // 對應SQL中的表名
}

// 活動報名表
model EventRegistration {
  id            Int       @id @default(autoincrement())
  exhibitionId  Int       // 關聯到展覽
  userName      String    // 報名者姓名
  userPhone     String?   // 報名者電話
  userEmail     String?   // 報名者信箱
  qrCode        String    @unique // 生成的 QR Code
  status        RegistrationStatus @default(REGISTERED) // 報名狀態
  registeredAt  DateTime  @default(now()) // 報名時間

  exhibition    Exhibition @relation(fields: [exhibitionId], references: [id], onDelete: Cascade)
  checkIns      CheckIn[]
  gameScores    GameScore[]

  @@map("event_registration")
}

// 簽到記錄表
model CheckIn {
  id              Int       @id @default(autoincrement())
  registrationId  Int       // 關聯到報名記錄
  roomId          String    // 房間ID (攤位識別)
  roomName        String    // 房間名稱
  checkInTime     DateTime  @default(now()) // 簽到時間
  ipAddress       String?   // 簽到IP地址

  registration    EventRegistration @relation(fields: [registrationId], references: [id], onDelete: Cascade)

  @@map("check_in")
}

// 遊戲分數記錄表
model GameScore {
  id              Int       @id @default(autoincrement())
  registrationId  Int       // 關聯到報名記錄
  roomId          String    // 房間ID
  gameType        GameType  @default(DART) // 遊戲類型
  score           Int       // 遊戲分數
  maxScore        Int?      // 最高分數
  playTime        Int?      // 遊戲時長(秒)
  isCompleted     Boolean   @default(false) // 是否完成遊戲
  playedAt        DateTime  @default(now()) // 遊戲時間

  registration    EventRegistration @relation(fields: [registrationId], references: [id], onDelete: Cascade)

  @@map("game_score")
}

// 房間管理表
model GameRoom {
  id            String    @id // 房間ID
  name          String    // 房間名稱
  exhibitionId  Int       // 關聯到展覽
  gameType      GameType  @default(DART) // 遊戲類型
  status        RoomStatus @default(ACTIVE) // 房間狀態
  maxPlayers    Int       @default(1) // 最大玩家數
  currentPlayers Int      @default(0) // 當前玩家數
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  exhibition    Exhibition @relation(fields: [exhibitionId], references: [id], onDelete: Cascade)

  @@map("game_room")
}

enum Role{
  USER
  ADMIN
  SUPERADMIN
}

enum VideoSource{
  YOUTUBE
  S3
  VIMEO
  LOCAL
}

enum VideoStatus{
  PENDING
  APPROVED
  INACTIVE
  ACTIVE
}

enum ProjectStatus{
  CLOSE
  OPEN
}

enum Game{
  LuckyDart
}

enum GameType{
  DART
  CLAW_MACHINE
  VENDING
}

enum RegistrationStatus{
  REGISTERED
  CHECKED_IN
  COMPLETED
  CANCELLED
}

enum RoomStatus{
  ACTIVE
  INACTIVE
  MAINTENANCE
}

enum Permissions{
  ALL
  VIEW
  EDIT
  DELETE
}