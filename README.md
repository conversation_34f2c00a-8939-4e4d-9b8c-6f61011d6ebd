# 廣告定時投放系統

## 目前製作進度

- 帳戶管理
- 專案管理
- 廣告排程
- 群組管理

### 帳戶管理製作進度

- 註冊 
- 登入 

### 專案管理製作進度

- 專案管理頁面 
- 專案每日定時啟動關閉
- 專案啟動並指定時間關閉

### 廣告排程製作進度

- 廣告排程頁面 (進行中)
- 廣告上傳
- 廣告刪除
- 廣告排程 API (進行中)

### 群組管理製作進度

- 群組管理頁面
- 創建 
- 刪除 
- 加入 

```sql
## Database
CREATE TABLE User(
    account varchar(255) NOT NULL,
    password varchar(255) NOT NULL,
    name varchar(255),
    phone int,
    email varchar(255),
    PRIMARY KEY(account)
);
CREATE TABLE Projects(
    id int NOT NULL auto_increment,
    name varchar(255) NOT NULL,
    time_start DATETIME,
    time_end DATETIME,
    PRIMARY KEY(id)
);

CREATE TABLE MemberGroup(
    id int NOT NULL auto_increment,
    name varchar(255),
    manager varchar(255),
    project_id int NOT NULL,
    PRIMARY KEY(id)
);

CREATE TABLE Permission(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    pid int NOT NULL,
    create_proj tinyint(1) NOT NULL DEFAULT 1,
    update_proj tinyint(1) NOT NULL DEFAULT 1,
    read_proj tinyint(1) NOT NULL DEFAULT 1,
    delete_proj tinyint(1) NOT NULL DEFAULT 1,
    PRIMARY KEY(id)
);
CREATE TABLE Member(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    gid int NOT NULL,
    PRIMARY KEY(id)
);
CREATE TABLE Media(
    id int NOT NULL auto_increment,
    account varchar(255) NOT NULL,
    pid int NOT NULL,
    url varchar(255) NOT NULL,
    PRIMARY KEY(id)
);

```
