version: '3.8'

services:
  # MySQL 資料庫
  mysql:
    image: mysql:8.0
    container_name: iamm-mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: iamm
      MYSQL_USER: iamm_user
      MYSQL_PASSWORD: iamm_password
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    networks:
      - iamm
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-ppassword"]
      timeout: 20s
      retries: 10
      interval: 10s

  # 後端 API 服務
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: iamm-backend
    ports:
      - "3061:3061"
    volumes:
      - ./backend:/app/backend
      - ./prisma:/app/prisma
      - /app/backend/node_modules
    working_dir: /app/backend
    environment:
      - NODE_ENV=development
      - DATABASE_URL=mysql://root:password@mysql:3306/iamm
      - JWT_SECRET=IAMMSecretPassword
    command: npm run docker:dev
    networks:
      - iamm
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy

  # 前端應用
  frontend:
    build:
      context: .
      dockerfile: app/Dockerfile
    container_name: iamm-frontend
    ports:
      - "5173:5173"
    volumes:
      - ./app:/app/frontend
      - /app/frontend/node_modules
    working_dir: /app/frontend
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3061
    command: npm run dev -- --host 0.0.0.0
    networks:
      - iamm
    restart: unless-stopped
    depends_on:
      - backend

volumes:
  mysql_data:
    driver: local

networks:
  iamm:
    driver: bridge
