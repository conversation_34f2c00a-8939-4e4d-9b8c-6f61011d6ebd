{"name": "iamm-backend", "version": "1.0.0", "description": "IAMM Backend API Server", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "docker:dev": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix"}, "dependencies": {"@prisma/client": "6.6.0", "express": "^4.21.0", "socket.io": "^4.8.0", "jsonwebtoken": "^9.0.2", "bcrypt": "^5.1.1", "cors": "^2.8.5", "body-parser": "^1.20.3", "qrcode": "^1.5.4", "uuid": "^10.0.0", "winston": "^3.17.0", "dotenv": "^16.4.5"}, "devDependencies": {"@types/express": "^5.0.0", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@types/node": "^20.0.0", "typescript": "^5.6.3", "ts-node": "^10.9.2", "nodemon": "^3.1.7", "jest": "^29.7.0", "@types/jest": "^29.5.0"}, "prisma": {"schema": "../prisma/schema.prisma"}}