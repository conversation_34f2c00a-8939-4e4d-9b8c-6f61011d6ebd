import fs from 'fs';
import path from "path";
import express from 'express';
import ViteExpress from "vite-express";
import https from 'https';
import cors from "cors";
import * as bodyParser from 'body-parser';
import { PrismaClient } from '@prisma/client';
import { uint32 } from 'aws-sdk/clients/iotfleetwise';
import * as resources_router from '../src/routes/resources_routes';
import * as connections_router from '../src/routes/connections_routes';
import * as video_router from '../src/routes/video_routes';
import { createProxyMiddleware } from 'http-proxy-middleware';
import { logger } from './winston/logger';

const app = express();

const prisma = new PrismaClient();

app.use(express.static(path.join(__dirname, '../../app/dist')));
// app.use(express.static('./reference/game/MetaTagDisplayClient/public'));
// app.use(express.static('./reference/vending_machine/displayer/public'));
// app.use(express.static('./reference/vending_machine/controller/public'));

app.use(cors());

// parse application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: true }))

// parse application/json
app.use(bodyParser.json())

app.use(resources_router.router);
app.use(connections_router.router);
app.use(video_router.router);

app.use('/libraries', createProxyMiddleware({
        target: 'http://127.0.0.1:3007/libraries',
        changeOrigin:true,
        pathRewrite:{
          "^/libraries":""
        }
    }));
app.use('/game/health', createProxyMiddleware({
  target:"http://127.0.0.1:3005/health",
  changeOrigin: true,
  pathRewrite:{
    "^/game/health":""
  }
}))
app.use('/game', createProxyMiddleware({
  target: 'http://127.0.0.1:3005',
  changeOrigin: true,
  pathRewrite:{
      '^/game':'',
      }
  }));

// app.use('/vending_displayer', createProxyMiddleware({
//   target: 'http://127.0.0.1:3008/vending_displayer',
//   changeOrigin: true,
//   pathRewrite:{
//       '^/vending_displayer':'',
//       }
//   }));

// app.use('/vending_displayer/health', createProxyMiddleware({
//   target: 'http://127.0.0.1:3008/health',
//   changeOrigin: true,
//   pathRewrite:{
//       '^/vending_displayer/health':'',
//       }
//   }));


//   app.use('/vending_controller', createProxyMiddleware({
//     target: 'http://127.0.0.1:3007/vending_controller',
//     changeOrigin: true,
//     pathRewrite:{
//         '^/vending_controller':'',
//         }
//     }));
  
//   app.use('/vending_controller/health', createProxyMiddleware({
//     target: 'http://127.0.0.1:3007/health',
//     changeOrigin: true,
//     pathRewrite:{
//         '^/vending_controller/health':'',
//         }
//     }));

const options = {
  key: fs.readFileSync("./localhost-key.pem"),
  cert: fs.readFileSync("./localhost.pem"),
};

const server = https.createServer(options, app).listen(3061, () => {
  logger.info("App listen in 3061");
});

ViteExpress.bind(app, server);

function listRoutes() {
    const routes: string[] = [];
    app._router.stack.forEach((middleware: { route: { path: any; methods: {}; }; name: string; handle: { stack: any[]; }; }) => {
      if (middleware.route) {
        // 如果是路由，則加入路由路徑和方法
        const routePath = middleware.route.path;
        const routeMethods = Object.keys(middleware.route.methods).join(', ').toUpperCase();
        routes.push(`${routeMethods} ${routePath}`);
      } else if (middleware.name === 'router') {
        // 如果是子路由，遞迴查找
        middleware.handle.stack.forEach((handler: { route: { path: string; methods: {}; }; }) => {
          const routePath = handler.route?.path || '';
          const routeMethods = handler.route ? Object.keys(handler.route.methods).join(', ').toUpperCase() : '';
          if (routePath) routes.push(`${routeMethods} ${routePath}`);
        });
      }
    });
    return routes;
  }
  
  // 打印所有路徑
  logger.info(listRoutes());

const interval = setInterval(() => {
  updateProjectsStatus();
}, 1000);

async function updateProjectsStatus(){
  // console.clear();
  //  logger.info("update Exhibition Status")
    const exhibitionsData = await prisma.exhibition.findMany({
        select: {
            id: true,
            status: true,
            start_time: true,
            end_time:true,
            expiration: true
        }
    });

    var date = new Date();
    // 修復：使用 24 小時制格式，與資料庫一致
    var current_time = date.toTimeString().substring(0, 5); // 格式: "HH:MM"
    const exhibitionsToClose:uint32[] = [];
    for(let i = 0; i < exhibitionsData.length; i++){
      // 查是否過期，如果是把專案關閉,移除有效期限。在有效期限內，自動開關不會奏效
      if(exhibitionsData[i].expiration){
        var expirationDateTime = exhibitionsData[i].expiration?? new Date();
        if(expirationDateTime <= date){
          const exhibition = await prisma.exhibition.update({
              where: {
                  id: exhibitionsData[i].id
              },
              data: {
                  status: "CLOSE",
                  expiration: null
              }
          }); 
          logger.info(`[index.js line 93]: close exhibition ${exhibition.id} at ${date}`);
        }
        continue;
      }
      if(!(exhibitionsData[i].start_time || exhibitionsData[i].end_time)){
          continue;
      }
      // 修復：直接使用 24 小時制時間，不需要轉換
      var start_time = exhibitionsData[i].start_time as string;
      var end_time = exhibitionsData[i].end_time as string;
      

        if(exhibitionsData[i].status == "CLOSE" && start_time == current_time){
            const exhibition = await prisma.exhibition.update({
                where: {
                    id: exhibitionsData[i].id
                },
                data: {
                    status: "OPEN"
                }
            });
        }
    
        if(exhibitionsData[i].status == "OPEN" && end_time == current_time){
          exhibitionsToClose.push(exhibitionsData[i].id);
        }
    }
  
    for(let exhibitionId of exhibitionsToClose){
      const exhibition = await prisma.exhibition.update({
          where: {
              id: exhibitionId
          },
          data: {
              status: "CLOSE"
          }
      });
  }
}