import express from 'express';
import * as UserController from "../controllers/user.controller";
import * as PermissionController from "../controllers/permission.controller";
import * as ProjectController from "../controllers/project.controller";
import * as ExhibitionController from "../controllers/exhibition.controller";
import * as VideoController from "../controllers/video.controller";
import * as MemberGroupController from "../controllers/memberGroup.controller";
import * as MemberController from "../controllers/member.controller";
import * as VideoPlayStatController from "../controllers/videoPlayStats.controller";
import {auth} from "../auth";
import { checkPermission } from '../middlewares/permissions';
import { Action } from '../constants/action';

declare global {
    namespace Express {
      interface Request {
        auth?: {
          username: string;
        };
      }
    }
  }

export const router = express.Router({ mergeParams: true })

router.post("/api/v1/user", UserController.createUser);

router.get("/api/v1/user/:account", auth.required, UserController.getUser);

router.get("/api/v1/users", auth.required, checkPermission(Action.LIST_USERS), UserController.getUsers);

router.put("/api/v1/user/:account", auth.required, UserController.updateUser);

router.delete("/api/v1/user/:account", auth.required, UserController.deleteUser);

router.get("/api/v1/user/:account/projects", auth.required,UserController.getUserProjects);

router.post("/api/v1/user/login",UserController.login);

router.get("/api/v1/user/secret", auth.required, UserController.secret);

// router.post("api/v1/user/logout/:account",UserController.logout);


router.post("/api/v1/permission",PermissionController.createPermission);

router.put("/api/v1/permission/:id",PermissionController.updatePermission);

router.get("/api/v1/permission/:id",PermissionController.getPermission);

router.get("api/v1/permissions", PermissionController.getPermissions);

router.delete("/api/v1/permission/:id", PermissionController.deletePermission);


router.post("/api/v1/project", auth.required, checkPermission(Action.CREATE_PROJECT), ProjectController.createProject);

router.put("/api/v1/project/:id", auth.required, checkPermission(Action.MODIFY_PROJECT), ProjectController.updateProject);

router.get("/api/v1/project/:id", auth.required, checkPermission(Action.VIEW_PROJECT), ProjectController.getProject);

router.get("/api/v1/projects", auth.required, ProjectController.getProjects);

router.delete("/api/v1/project/:id", auth.required, checkPermission(Action.DELETE_PROJECT), ProjectController.deleteProject);

router.get("/api/v1/project/:id/permissions", auth.required, ProjectController.getProjectPermissions);

router.get("/api/v1/project/:id/memberGroups", auth.required, ProjectController.getProjectMemberGroups);

router.get("/api/v1/project/:id/exhibitions", auth.required, ProjectController.getProjectExhibitions);


router.post("/api/v1/exhibition", auth.required, ExhibitionController.createExhibition);

router.put("/api/v1/exhibition/:id", auth.required, ExhibitionController.updateExhibition);

router.get("/api/v1/exhibition/:id", auth.required, ExhibitionController.getExhibition);

router.get("/api/v1/exhibitions", auth.required, ExhibitionController.getExhibitions);

router.delete("/api/v1/exhibition/:id", auth.required, ExhibitionController.deleteExhibition);

router.get("/api/v1/exhibition/:id/videos", auth.required, ExhibitionController.getExhibitionVideos);


router.post("/api/v1/member", auth.required, MemberController.createMember);

router.put("/api/v1/member", auth.required, MemberController.updateMember);

router.get("/api/v1/member/:id", auth.required, MemberController.getMember);

router.get("/api/v1/members", auth.required, MemberController.getMembers);

router.delete("/api/v1/member/:id", auth.required, MemberController.deleteMember);


router.post("/api/v1/memberGroup", auth.required, MemberGroupController.createMemberGroup);

router.put("/api/v1/memberGroup", auth.required, MemberGroupController.updateMemberGroup);

router.get("/api/v1/memberGroup/:id", auth.required, MemberGroupController.getMemberGroup);

router.get("/api/v1/memberGroups", auth.required, MemberGroupController.getMemberGroups);

router.delete("/api/v1/memberGroup/:id", auth.required, MemberGroupController.deleteMemberGroup);

router.get("/api/v1/memberGroup/:id/members", auth.required, MemberGroupController.getMemberGroupMembers);


router.get("/api/v1/memberGroup/:id/permissions", auth.required, MemberGroupController.getMemberGroupPermissions)

router.post("/api/v1/video", auth.required, VideoController.createVideo);

router.put("/api/v1/video/:id", auth.required, VideoController.updateVideo);

router.get("/api/v1/video/:id", auth.required, VideoController.getVideo);

router.get("/api/v1/videos", auth.required, VideoController.getVideos);

router.delete("/api/v1/video/:id", auth.required, VideoController.deleteVideo);

router.get('/api/v1/videos/videoPlayStats', auth.required, VideoController.getVideosWithPlayStat);

router.post("/api/v1/videoPlayStat", VideoPlayStatController.createVideoPlayStat);

router.put("/api/v1/videoPlayStat/:id", VideoPlayStatController.updateVideoPlayStat);

router.get("/api/v1/videoPlayStat/:id",auth.required, VideoPlayStatController.getVideoPlayStat);

router.get("/api/v1/videoPlayStats", auth.required,  VideoPlayStatController.getVideoPlayStats);

router.delete("/api/v1/videoPlayStat/:id", auth.required, VideoPlayStatController.deleteVideoPlayStat);