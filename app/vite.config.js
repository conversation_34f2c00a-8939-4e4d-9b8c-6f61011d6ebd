import {defineConfig} from 'vite'

export default defineConfig({
    build:{
        rollupOptions:{
            input:{
                main: './index.html',
                project_list:'./html/project/list.html',
                project_create:'./html/project/create.html',
                project_details:'./html/project/details.html',
                project_monitoring:'./html/project/monitoring.html',
                project_settings:'./html/project/settings.html',
                project_statistics:'./html/project/statistics.html',
                project_dashboard:'./html/dashboard.html',
                user_details:"./html/user/details.html",
                user_register:"./html/user/register.html",
                user_settings:"./html/user/settings.html",
                exhibition_create:"./html/exhibition/create.html",
                exhibition_details:"./html/exhibition/details.html",
                exhibition_list:"./html/exhibition/list.html",
                exhibition_monitoring:"./html/exhibition/monitoring.html",
                exhibition_settings:"./html/exhibition/settings.html",
                exhibition_statistics:"./html/exhibition/statistics.html",
                exhibition_view:"./html/exhibition/view.html",
                product_list:"./html/game/product-list.html",
                user_list:"./html/game/user-list.html",
                voucher_list:"./html/game/voucher-list.html",
                group_create:"./html/group/create.html",
                group_list:"./html/group/list.html",
                video_displayer:"./html/video/displayer.html",
                video_list:"./html/video/list.html",
                video_upload:"./html/video/upload.html",
                service_list:"./html/service/list.html",
                claw_controller:"./claw_machine/controller/index.html"
            }
        }
    }
})