<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Login</title>
        <link rel="stylesheet" href="css/styles.css">
        <link rel="stylesheet" href="css/indexstyle.css">
        <script type='module' src="/src/translation.js"></script>
        <script type='module' src="/src/utils/errorMonitor.js"></script>
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div class="loginBox">
            <img class="loginimg" src="html/assets/user_big_white.png">
            <h1 data-i18n="login" class="loginTitle">Login</h1>
            <form id = "login_form">
                <div>
                    <!--label data-i18n="account" for="account">Account</label-->
                    <input type="text" name="account" id="account" placeholder="Account" required />
                </div>
               
                <div>
                    <!--label data-i18n="password" for="password">Password</label-->
                    <input type="password" name="password" id="password" placeholder="Password" required />
                </div>
                

                <label id="error_message" style="color:red"></label>
                <div  class="button-container">
                    <input data-i18n="submit" type="submit" value="submit"/>
                    <input data-i18n="register" type="button" value="register" id="registerBtn">
                </div>
                
            </form>
        </div>
        

    
        <script type="module">
            import {translationText, setLanguage} from "./src/translation.js";
            import {linkApiRoot} from './src/utils/env.js';

            const form = document.getElementById('login_form'); 
            
            document.getElementById('registerBtn').addEventListener('click', toRegisterPage);

            function toRegisterPage(){
                window.location.href = "./html/user/register.html";
            }

            form.addEventListener('submit', async function(event) {
                const formData = new FormData(form);
                event.preventDefault(); // 阻止表單的默認提交行為
                const errorMessage = document.getElementById('error_message');
        
                // 建立表單數據
                var object = {};
                formData.forEach(function(value, key){
                    object[key] = value;
                });
                var json = JSON.stringify(object);
                // 使用 Fetch 提交表單
                await fetch(linkApiRoot('user/login'), {
                    method: 'POST',
                    headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                    },
                    body: json
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error("Invalid username or password.");
                    }
                    return response.json();
                })
                .then(data => {
                    // 顯示伺服器回應
                    localStorage.setItem('account', object['account']);
                    localStorage.setItem('token', data.token);
                    window.location.href = "./html/project/list.html";
                })
                .catch(error => {
                    errorMessage.innerText = error;
                });
        });
          </script>
    </body>
</html>