<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Dashboard</title>
        <link rel="stylesheet" href="../../css/styles.css">
    <script type='module' src="/src/translation.js"></script>
        <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
        <link rel="stylesheet" href="../../css/project-list.css">
        <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label class="title">TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div class="project-list-place">
            <div class="top-title">
                <p class="list-title">專案列表</p>
                <input type="button" id="create_project_btn"></input>
                <input type="button" id="delete_project_btn"></input>
                <input type="button" value="Join" id="join_group_btn"></input>
            </div>

            <table id="projects-table">
                <thead id="title">
                    <tr>
                        <th data-i18n="id">ID</th>
                        <th data-i18n="name">Name</th>
                        <th data-i18n="status">Status</th>
                        <th data-i18n="created_by">CreatedBy</th>
                    </tr>
                </thead>
                <tbody id="content"></tbody>
            </table>
        </div>
        

        <script src="../../src/project/projects.js" type="module"></script>
    </body>
</html>