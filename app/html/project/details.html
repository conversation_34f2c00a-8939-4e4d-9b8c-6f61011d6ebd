<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Project Details</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/project-details.css">
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
                <button class="thistime" onclick="window.location.href='../project/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
                <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
            </div>
            <p class="projectName">［專案名稱］</p>
            <div class="project-details-maincontent">
                <h2 class="title-name">詳細資訊</h2>
                <div class="details-header">
                    <div id="descriptionText"></div>
                </div>

                <!-- <div>
                    <label>expiration time:</label>
                    <label id="expirationTimeText"></label>
                </div>
                <div>
                    <b>Daily opening and closing times</b>
                    <br>
                    <label id="opening-closing-times"></label>
                    <label for="startTimeText">Start Time:</label>
                    <label id="startTimeText">123</label>   
                    <br>
                    <label for="endTimeText">End Time:</label>
                    <label  id="endTimeText"></label>
                </div> -->
                <!-- <canvas id="qr-container"></canvas> -->
                <!-- <script src="../../src/bundle.js"></script> -->
                 <script type="module" src="../../src/project/title.js"></script>
                <script type="module" src="../../src/project/details.js"></script>
                
            </div>
        </div>
    </body>
</html>
