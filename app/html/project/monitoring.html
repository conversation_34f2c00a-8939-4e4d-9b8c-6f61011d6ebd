<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Project Details</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/project-monitoring.css">
        <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
                <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
                <button class="thistime" onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
                <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
            </div>
            <p class="projectName">［專案名稱］</p>
            <div class="project-monitoring-maincontent">
                <h2 class="title-name">運行狀態監控</h2>
                <div>
                    <input type="button" id="export_data_btn" value="export"></input>
                    <label><span data-i18n="connects" id="connects_label">connects</span>:</label>
                    <label id="connect-amount"></label>
                </div>

                <div>
                    <table id="displayers-table" class="table">
                        <thead>
                            <tr id="displayers-title">
                                <th data-i18n="exhibition_name">Exhibition Name</th>
                                <th data-i18n="id">ID</th>
                                <th data-i18n="ip">IP</th>
                                <th data-i18n="status">Status</th>
                            </tr>
                        </thead>
                        <tbody id="displayers-content"></tbody>
                    </table>
                </div>
                <script type="module" src="../../src/project/title.js"></script>
                <script type="module" src="../../src/project/monitoring.js"></script>
            </div>
        </div>
    </body>
</html>
