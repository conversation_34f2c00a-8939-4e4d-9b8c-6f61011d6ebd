<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Project Dashboard</title>
    <link rel="stylesheet" href="../css/styles.css">
    <script type='module' src="/src/translation.js"></script>
    <link rel="stylesheet" href="../css/dashboard.css">
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.3/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.6/css/responsive.dataTables.min.css">
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/socket.io-client@4.8.0/dist/socket.io.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <p class="projectName">［專案名稱］</p>
    <nav class="maincontent">
        <button class="mainbtn" data-i18n="back_to_list" id="backBtn">返回儀錶板</button>
        <button class="mainbtn" data-i18n="details" id="detailsBtn"></button>
        <button class="mainbtn" data-i18n="groups_with_members" id="operationBtn"></button>
        <button class="mainbtn" data-i18n="exhibitions" id="exhibitionBtn"></button>
        <button class="mainbtn" data-i18n="monitoring" id="monitoringBtn"></button>
        <button class="mainbtn" data-i18n="statistics" id="statisticsBtn"></button>
        <button class="mainbtn" data-i18n="settings" id="settingBtn">設定</button>
    </nav>

    <!-- 動態載入內容的區域 -->
    <section id="content-section">
    </section>
    <script type="module" src="../src/project/title.js"></script>
    <script type="module" src="../src/project/dashboard.js"></script>
</body>
</html>