<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>Exhibition List</title>
        <link rel="stylesheet" href="../../css/styles.css">
        <link rel="stylesheet" href="../../css/exhibition-list.css">
        <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
        <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
        <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
    </head>
    <body>
        <header>
            <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
        </header>
        <div>
            <div class="btn-list">
                <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
                <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
                <button class="thistime" onclick="window.location.href='../exhibition/list.html';">展示廳管理</button>
                <button onclick="window.location.href='../group/list.html';">人員與群組管理</button>
            </div>
            <p class="projectName">［專案名稱］</p>
            <div class="exhibition-list-maincontent">
                <div class="top-title">
                    <h2 class="title-name">展示廳管理</h2>
                    <input type="button" id="create_btn"></input>
                    <input type="button" id="delete_btn"></input>
                </div>
                
                <table id="table">
                    <thead>
                        <tr id="titleColumns">
                            <th>ID</th>
                            <th>Name</th>
                            <th>Status</th>
                            <th>Game</th>
                        </tr>
                    </thead>
                    <tbody id="content"></tbody>
                </table>
            </div>
        </div>

        <script type="module" src="../../src/project/title.js"></script>
        <script src="../../src/exhibition/list.js" type="module"></script>
    </body>
</html>
