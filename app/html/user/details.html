<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/user-details.css">
    <script type='module' src="/src/translation.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div class="details">
        <div class="detailbox">
            <img class="loginimg" src="../assets/profile_big_blue.png">
            <div class="databox">
                <div class="details-item">
                    <label><span data-i18n="name">Name</label>
                    <label id="nameText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="email">Email</label>
                    <label id="emailText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="phone">Phone</label>
                    <label id="phoneText" class="details-content"></label>
                </div>
                <div class="details-item">
                    <label><span data-i18n="role">Role</label>
                    <label id="roleText" class="details-content"></label>
                </div>
            </div>
            
        </div>
        
    </div>
    

    <script type="module">
        import * as tokenOps from '../../src/user/tokenOps.js';
        import {handleError} from '../../src/handle_error.js';
        import {linkApiRoot} from '../../src/utils/env.js';
        var token = tokenOps.getToken();
        var nameText = document.getElementById('nameText');
        var emailText = document.getElementById('emailText');
        var phoneText = document.getElementById('phoneText');
        var roleText  = document.getElementById('roleText');
        var account = localStorage.getItem('account')
        window.onload = load;
        function load(){
            fetch(linkApiRoot('user/') + account,
        {
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(userData =>{
            console.log(userData)
            nameText.textContent = userData.name;
            emailText.textContent = userData.email;
            phoneText.textContent = userData.phone;
            roleText.textContent = userData.role;
        })
        .catch(err=>{
            console.error(err);
        })
        }
    </script>
</body>
</html>