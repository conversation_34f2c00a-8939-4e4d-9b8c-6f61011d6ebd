<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/user-settings.css">
    <script type='module' src="/src/translation.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
        <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
        <a id="openUserDialog" data-i18n="user" href="">User</a>
        <div class="dialog-container">
            <dialog id="userDialog">
                <ul>
                    <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                    <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                    <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                </ul>   
            </dialog>
        </div>
            <a id="languageBtn" data-i18n="language" href="">Language</a>
            <div class="dialog-container">
                <dialog id="langDialog">
                    <ul>
                        <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                        <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                    </ul>
                </dialog>
            </div>
        <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <form id="edit-form">
        <div class="editbox">
            <img class="loginimg" src="../assets/profile_big_blue.png">
            <div class="formbox">
                <div class="form-item">
                    <label><span data-i18n="name">Name</label>
                    <input name="name" type="text" id="nameInput"></input>
                </div>
                <div class="form-item">
                    <label><span data-i18n="email">Email</label>
                    <input name="email" type="text" id="emailInput"></input>
                </div>
                <div class="form-item">
                    <label><span data-i18n="phone">Phone</label>
                    <input name="phone" type="text" id="phoneInput"></input>
                </div>
            </div>
        </div>
        
        
        <label id="error" style="color:red"></label>
        <button data-i18n="submit" type="submit" id="submit">Submit</button>
    </form>

    <script type="module">
        import * as tokenOps from '../../src/user/tokenOps.js';
        import {handleError} from '../../src/handle_error.js';
        import {linkApiRoot} from '../../src/utils/env.js';

        var token = tokenOps.getToken();
        var nameInput = document.getElementById('nameInput');
        var emailInput = document.getElementById('emailInput');
        var phoneInput = document.getElementById('phoneInput');
        var account = localStorage.getItem('account');
        var error = document.getElementById('error');
        var editForm = document.getElementById('edit-form');
        window.onload = load;
        function load(){
            fetch(linkApiRoot('user/') + account,
        {
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(userData =>{
            nameInput.value = userData.name;
            emailInput.value = userData.email;
            phoneInput.value = userData.phone;
        })
        .catch(err=>{
            console.error(err);
        })
        }
        editForm.addEventListener("submit", function(event) {
            event.preventDefault();
            const formData = new FormData(editForm);
            var object = {};
            formData.forEach(function(value, key){
                object[key] = value;
            });
            var json = JSON.stringify(object);
            console.log(object);
            fetch('user/' + account,
            {
                method: 'PUT',
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json',
                    Authorization: 'Bearer '+ token 
                },
                body: json
            }
            )
            .then(handleError)
            .then(response => response.json())
            .then(data => {
                window.location.href = "./details.html";
            })
            .catch(err=>{
                error.textContent = err;
            });
        });
    </script>
</body>
</html>