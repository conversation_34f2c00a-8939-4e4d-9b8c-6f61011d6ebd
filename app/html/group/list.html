<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Group List</title>
    <link rel="stylesheet" href="../../css/styles.css">
    <link rel="stylesheet" href="../../css/group-list.css">
    <script src="https://code.jquery.com/jquery-3.5.1.js"></script>
    <script src="https://cdn.datatables.net/1.11.3/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.6/js/dataTables.responsive.min.js"></script>
</head>
<body>
    <header>
        <label>TIANYEN</label>
            <a id="projectsBtn" data-i18n="projects" href="/html/project/list.html">Projects</a>
            <a id="openUserDialog" data-i18n="user" href="">User</a>
            <div class="dialog-container">
                <dialog id="userDialog">
                    <ul>
                        <li><a id="informationBtn" data-i18n="information" href="/html/user/details.html">Information</a></li>
                        <li><a id="settingsBtn" data-i18n="settings" href="/html/user/settings.html">Settings</a></li>
                        <li><a id="signInAndOutBtn" data-18n="sign_in" href="">SignOut</a></li>
                    </ul>   
                </dialog>
            </div>
                <a id="languageBtn" data-i18n="language" href="">Language</a>
                <div class="dialog-container">
                    <dialog id="langDialog">
                        <ul>
                            <li><a id="ENBtn" data-i18n="english" href="">English</a></li>
                            <li><a id="ZHTWBtn" data-i18n="zh-tw" href="">zh-TW</a></li>
                        </ul>
                    </dialog>
                </div>
            <script type="module" src="/src/user/userDialog.js"></script>
    </header>
    <div>
        <div class="btn-list">
                <button onclick="window.location.href='../dashboard.html';">Back To Menu</button>
                <button onclick="window.location.href='../project/details.html';">詳細資訊</button>
                <button onclick="window.location.href='../project/statistics.html';">數據統計</button>
                <button onclick="window.location.href='../project/monitoring.html';">運行狀態監控</button>
                <button onclick="window.location.href='../exhibition/list.html';">展覽館管理</button>
                <button class="thistime" onclick="window.location.href='../group/list.html';">人員與群組管理</button>
            </div>
        <p class="projectName">［專案名稱］</p>
        <div class="group-list-maincontent">
            <h2 class="title-name">人員與群組管理</h2>
            <div class="groupContent">
                <div class="group-list">
                    <div id="operations" class="top-title">
                        <p data-i18n="group" class=list-title>群組</p>
                        <button id="create_btn">創建</button>
                        <button id="delete_btn">刪除</button>
                        <button id="leave_btn">離開</button>
                    </div>
                    <table id="groups-table" class="table">
                        <thead>
                            <tr id="groups-title">
                                <th>ID</th>
                                <th>Name</th>
                                <th>invite code</th>
                            </tr>
                        </thead>
                        <tbody id="groups-content"></tbody>
                    </table>
                </div>

                <div class="member-list">
                    <p class=memberlist-title>人員</p>
                    <table id="members-table" class="table">
                        <thead id="members-title">
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody id="members-content"></tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
    <script type="module" src="../../src/project/title.js"></script>
    <script type="module" src="../../src/group/groups.js"></script>
</body>

<!--div id="operations">
    <button id="create_btn">創建</button>
    <button id="delete_btn">刪除</button>
    <button id="leave_btn">離開</button>
</div>
<div>
    <div class="left">
        <h2 data-i18n="group">群組</h2>
        <table id="groups-table" class="left">
            <thead>
                <tr id="groups-title">
                    <th>ID</th>
                    <th>Name</th>
                    <th>invite code</th>
                </tr>
            </thead>
            <tbody id="groups-content"></tbody>
        </table>
    </div>
    
    <div class="right">
        <p id="invite-code"></p>
        <h2 data-i18n="member">人員</h2>
        <table id="members-table">
            <thead>
                <tr id="members-title">
                    <th>ID</th>
                    <th>Name</th>
                </tr>
            </thead>
            <tbody id="members-content"></tbody>
        </table>
    </div>
    
    <script type="module" src="../../src/group/groups.js"></script>
</div-->