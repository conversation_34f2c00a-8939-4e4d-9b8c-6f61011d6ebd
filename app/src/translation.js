import { getCookie, set<PERSON><PERSON>ie } from "./utils/cookie.js";
import { linkRoot } from "./utils/env.js";
import i18n from "./utils/i18n.js";

let initialized = false;
export function init(){
    const locale = getCookie("locale");
    console.log(locale)
    i18n.set({"resource":linkRoot("locales/translation.json"),"locale": locale ?? "en"})
        .init(async function (dict) {
            await this.translate();
    });
    initialized = true;
}

export function translationText(key){
    if(!initialized) 
        init();
    return i18n._(key);
}

export function setLanguage(lang){
    if(!initialized) 
        init();
    i18n.set({"locale": lang}).init(async function (dict){
        await this.translate();
    });
    setCookie("locale", lang, 365)
    window.location.reload();
}

init();