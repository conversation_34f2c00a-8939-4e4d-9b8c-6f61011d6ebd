import * as tokenOps from './user/tokenOps.js';
import {handleError} from './handle_error.js';
import { linkApiRoot } from './utils/env.js';
import e from 'cors';

const token = tokenOps.getToken();

let sources = {
    "iamm":linkApiRoot(""),
    "pd0001":"http://127.0.0.1:4000/api/"
}

export function remove(table, name, object){
    let result = [];
    $(`${name} tbody tr.selected`).each(function () {
        var rowData = table.row(this).data();  // 獲取該行的數據
        fetch(linkApiRoot(`${object}/${rowData[0]}`),
        {
            method: 'DELETE',
            headers:{ Authorization: 'Bearer '+ token }
        }
        )
        .then(handleError)
        .then(response => response.json()) 
        .then(videoData =>{
            result.push(videoData);
            var row = table.row($(this));
            row.remove().draw();  // 刪除該行並重新繪製表格
        })
        .catch(error => {
            console.error(error);
        });
    });
    return result;
}

// 一體化資料表設置(不包括remove)
export function table(tableName, objects, data, columns, where, doubleClickEffect, source = "iamm", columnDefs = {}, updateInterval = 0){
    let table = initTable(tableName, columnDefs);
    // let updateTableFunc = ()=>{
    //     updateTable(table, objects, columns, where, source);
    // }
    updateTableFunc();
    if(updateInterval > 0)
        setInterval(updateTableFunc, updateInterval);
    if(doubleClickEffect)
        doubleClickTable(table, tableName,doubleClickEffect);
    
}

export function initTable(name, columnDefs)  {
    let table = $(name).DataTable({
            "dom": 'rtip',
            columnDefs: columnDefs,
            autoWidth: false
        });
    
   
    $(`${name} tbody`).on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            $(this).addClass('selected');
        }
        });
    return table;
    }

export function initTableWithValues(name, columnDefs, data, columns)  {
    let table = $(name).DataTable({
        "dom": 'rtip',
        columnDefs: columnDefs,
        data: data,
        columns: columns,
        autoWidth: false
    });
    $(`${name} tbody`).on('click', 'tr', function () {
        if ($(this).hasClass('selected')) {
            $(this).removeClass('selected');
        }
        else {
            $(this).addClass('selected');
        }
        });
    return table;
    }


export function doubleClickTable(table, name, effect = (data)=>{}){
    $(`${name} tbody`).on('dblclick', 'tr', function () {
        var rowData = table.row(this).data();
        if(!rowData)
            return;
        effect(rowData);
    });
}

export function updateTable(table, objects, columns, query, source = "iamm"){
    let headers = source == "iamm"? { Authorization: 'Bearer '+ token }:{};
    //console.log(`${sources[source]}${objects.join("/")}${`?${query}`??""}`)
    fetch(`${sources[source]}${objects.join("/")}${query?`?${query}`:""}`,
        {
            method: 'GET',
            headers:headers
        }
        )
        .then(handleError)
        .then(response => {
            return response.json();
        }) 
        .then(data =>{   
            console.log(data);
            var table_data = [];
            var update_data = convertToList(data);
            table.rows().every(function(idx, tableLoop, rowLoop){
                var rowData = this.data();
                table_data.push({index:rowData[0],data: rowData, row:this});
            });
            //updata or add rows.
            const tableDataMap = new Map(table_data.map(item => [item.index, item]));
            update_data.forEach(item => {
                if (tableDataMap.has(item.index)) {
                    const tableItem = tableDataMap.get(item.index);
                    columns.forEach((column, i) => {
                        tableItem.data[i] = item.data[column];
                    });
                } else {
                    table.row.add(columns.map(column => item.data[column]));
                }
            });
            // Remove rows not in update_data
            table_data.forEach(item => {
                if (!update_data.find(update_item => update_item.index === item.index)) {
                    item.row.remove();
                }
            });
            table.draw();
        })
        .catch(error =>{
            console.error(error);
            return 0;
        })
}

function convertToList(data){
    let update_data = [];
    if(typeof(data.items) == 'string')
        data.items = JSON.parse(data.items);
    if(isArray(data)){
        data.forEach((item)=>{
            update_data.push({index:item['id'], data:item})
        })
    }
    if(isArray(data.items)){
        data.items.forEach((item)=>{
            update_data.push({index:item['id'], data:item})
        })
    }else if(isDictionary(data.items)){
        for(let key in data.items){
            if (data.items.hasOwnProperty(key)) {
                update_data.push({index:data.items[key]['id'], data: data.items[key]});
            }
        }
    }else{
        throw Error("Type Error.");
    }
    return update_data;
}
//TODO: adding connect amount feature will update in the future.
export function getListAmount(table, objects, columns, query, source = "iamm"){
    let headers = source == "iamm"? { Authorization: 'Bearer '+ token }:{};
    //console.log(`${sources[source]}${objects.join("/")}${`?${query}`??""}`)
    fetch(`${sources[source]}${objects.join("/")}${query?`?${query}`:""}`,
        {
            method: 'GET',
            headers:headers
        }
        )
        .then(handleError)
        .then(response => {
            return response.json();
        }) 
        .then(data =>{   
            var update_data = [];
            var table_data = [];
            if(typeof(data.items) == 'string')
                data.items = JSON.parse(data.items);
            if(isArray(data)){
                data.forEach((item)=>{
                    update_data.push({index:item['id'], data:item})
                })
            }
            if(isArray(data.items)){
                data.items.forEach((item)=>{
                    update_data.push({index:item['id'], data:item})
                })
            }else if(isDictionary(data.items)){
                for(let key in data.items){
                    if (data.items.hasOwnProperty(key)) {
                        update_data.push({index:data.items[key]['id'], data: data.items[key]});
                    }
                }
            }else{
                throw Error("Type Error.");
            }
            table.rows().every(function(idx, tableLoop, rowLoop){
                var rowData = this.data();
                table_data.push({index:rowData[0],data: rowData, row:this});
            });
            //updata or add rows.
            const tableDataMap = new Map(table_data.map(item => [item.index, item]));
            update_data.forEach(item => {
                if (tableDataMap.has(item.index)) {
                    const tableItem = tableDataMap.get(item.index);
                    columns.forEach((column, i) => {
                        tableItem.data[i] = item.data[column];
                    });
                } else {
                    table.row.add(columns.map(column => item.data[column]));
                }
            });
            // Remove rows not in update_data
            table_data.forEach(item => {
                if (!update_data.find(update_item => update_item.index === item.index)) {
                    item.row.remove();
                }
            });
            table.draw();
        })
        .catch(error =>{
            console.error(error);
            return 0;
        })
}

function isArray(obj) {
    return obj && typeof obj === 'object' && 'length' in obj;
}
  
function isDictionary(obj) {
    return obj && typeof obj === 'object' && !('length' in obj);
}