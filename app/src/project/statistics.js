import * as tokenOps from '../user/tokenOps.js';
import * as tableOps from '../table.js';
import { exportData } from '../export.js';
import {translationText} from "../translation.js";

var token = tokenOps.getToken();
// const socket = io('https://localhost:3001');
var data = JSON.parse(localStorage.getItem("project_data"));
var exportDataBtn = document.getElementById('export_data_btn');
var titles = document.getElementById("titles");
var where = encodeURIComponent(JSON.stringify({ pid:data[0] }));
var titleTranslationTexts;

document.onload=async()=>{
    exportDataBtn.value = await translationText("export");
    titleTranslationTexts = [await translationText("exhibition_name"), await translationText("name"), await translationText("impressions"), await translationText("time_spent"), await translationText("duration")];
    for(let i=0;i < titles.children.length;i++){
        titles.children.item(i).textContent = titleTranslationTexts[i];
    }
}

var table = tableOps.initTable('#statistics-table');
tableOps.updateTable(table,['videos','videoPlayStats'],['ExhibitionName','name','impressions', "TimeSpent","duration"], `pid=${data[0]}`);
setInterval(()=>{
    tableOps.updateTable(table,['videos','videoPlayStats'],['ExhibitionName','name','impressions', "TimeSpent","duration"], `pid=${data[0]}`);
},3000);

exportDataBtn.addEventListener('click',function(){
    exportData('videos/videoPlayStats');
});

