import * as tokenOps from './tokenOps.js';
import {translationText, setLanguage} from "../translation.js";


var openDialogButton = document.getElementById('openUserDialog');
var userDialog = document.getElementById('userDialog');
var langDialog = document.getElementById('langDialog');
var signBtn = document.getElementById('signInAndOutBtn');
var informationBtn = document.getElementById('informationBtn');
var projectsBtn = document.getElementById('projectsBtn');
var settingsBtn = document.getElementById('settingsBtn');
var langBtn = document.getElementById("languageBtn");
var enBtn = document.getElementById('ENBtn');
var zhtwBtn = document.getElementById("ZHTWBtn")
var token = tokenOps.getToken();
window.onload = async ()=>{
    if(token){
        signBtn.textContent = await translationText("sign_out");
        informationBtn.style.display = 'block';
        projectsBtn.style.display = 'block';
        settingsBtn.style.display = 'block';
    }
    else{
        signBtn.textContent = await translationText("sign_in");
        informationBtn.style.display = 'none';
        projectsBtn.style.display = 'none';
        settingsBtn.style.display = 'none';
    }
}


openDialogButton.addEventListener('click',(event)=>{
    event.preventDefault();
    if(userDialog.hasAttribute('open')){
        userDialog.close();
        return;
    }
    const rect = openDialogButton.getBoundingClientRect();
    if(token){
        userDialog.style.left = `${rect.left - 237}px`;
    }else{
        userDialog.style.left = `${rect.left - 85}px`;
    }
    userDialog.show();
});

userDialog.addEventListener('focusout',(event)=>{
    if(!userDialog.contains(event.relatedTarget)){
        userDialog.close();
    }
});

langDialog.addEventListener('focusout', (event)=>{
    if(!langDialog.contains(event.relatedTarget)){
        langDialog.close();
    }
})

signBtn.addEventListener('click', (event)=>{
    event.preventDefault();
    let backToLogin = true;
    if(signBtn.textContent == "Sign Out"){
        backToLogin = confirm('Are you sure you want to log out?');
    }
    if(backToLogin){
        localStorage.clear();
        window.location.href = '../../index.html';
    }
});

langBtn.addEventListener('click',(event)=>{
    event.preventDefault();
    if(langDialog.hasAttribute('open')){
        langDialog.close();
        return;
    }
    const rect = langBtn.getBoundingClientRect();
    langDialog.style.left = `${rect.left - 50}px`;
    
    langDialog.show();
});

enBtn.addEventListener('click', (event)=>{
    event.preventDefault();
    setLanguage('en');
})

zhtwBtn.addEventListener('click', (event)=>{
    event.preventDefault();
    setLanguage('tw');
})

function updateDialogPosition(){
    const rect = userDialog.getBoundingClientRect();
    const rect2 = langDialog.getBoundingClientRect();

    const offsetRight = 16;
    const offsetRight2 = 155;

    const leftPosition = window.innerWidth - rect.width - offsetRight;
    const leftPosition2 = window.innerWidth - rect2.width - offsetRight2;

    userDialog.style.left = `${leftPosition}px`;
    langDialog.style.left = `${leftPosition2}px`;
}
window.addEventListener('resize', updateDialogPosition);