
label{
	color: white;
}

.title{
	color: #6F8FF6;
}

.list-title{
	color: #6F8FF6;
	font-size: 20px;
	font-weight: bold;
}
.top-title{
	display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.project-list-place{
	position: relative;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 30px;
	width: 86vw;
	left: 50vw;
	transform: translate(-50%, 0);
    align-items: center;
	background-color: #202020;
	border-radius: 20px;
	margin-top: 10vw;
}
#projects-table{
	width: 100%;
	color:white;
	padding-top: 10px;
}
#create_project_btn{
	background-color: transparent;
	/*font-size: 20px;*/
	width: 18px;
	height: 18px;
	color: #6F8FF6;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
    background-image: url(../html/assets/add_blue.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#delete_project_btn{
	background-color: transparent;
	/*font-size: 20px;*/
	width: 18px;
	height: 18px;
	color: #6F8FF6;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
    background-image: url(../html/assets/bin_blue.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#join_group_btn{
	background-color: transparent;
	font-size: 20px;
	color: #6F8FF6;
	border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}
/*
.loginBox{
	width: 300px;
	border-radius: 20px;
	position: relative;
	transform: translate(-50%, 0);
	left: 50%;
	top: 10vw;
	background-color: #202020;
	padding: 10vw 2vw 2vw 2vw;
}
*/
