label{
	color: #6F8FF6;
}

.loginTitle{
	color: white;
	text-align: center;
	font-family: Arial, sans-serif;
	font-size: 20px;
}

.loginBox{
	width: 300px;
	border-radius: 20px;
	position: relative;
	transform: translate(-50%, 0);
	left: 50%;
	top: 10vw;
	background-color: #151515;
	padding: 5vw 2vw 2vw 2vw;
}
.loginimg{
    position: relative;
    left: 50%;
    transform: translate(-50%, 0);
    width: 100px;

}
.button-container {
    display: flex; /* 使用 Flexbox 排列子元素 */
    justify-content: center; /* 讓子元素在水平方向置中 */
    align-items: center; /* 垂直方向置中（如果需要） */
    gap: 20px; /* 按鈕之間的間距 */
}
input[type="submit"],
input[type="button"] {
    display: block; /* 让按钮成为块级元素，才会生效居中 */
    margin: 20px auto; /* 上下间距 20px，左右自动居中 */
    margin-top: 40px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: #151515;
    color: #6F8FF6; /* 白色文字 */
    border-color: #6F8FF6;
    border-radius: 24px; /* 圆角按钮 */
    cursor: pointer; /* 鼠标悬浮变成手势 */
    width: 130px;
}
/*
#languageBtn{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}
#openUserDialog{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}
#projectsBtn{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}
*/