label{
	color: #6F8FF6;
}
body{
    color: white;
}
.projectName{
    position: relative;
    width: 20vw;
    left: 10vw;
    text-align: left;
    font-size: 30px;
}
.exhibitionName{
    position: relative;
    width: 20vw;
    left: 10vw;
    text-align: left;
    font-size: 30px;
}

.maincontent {
    display: flex;
    justify-content: center; /* 水平置中 */
    align-items: center; /* 垂直置中 */
    flex-wrap: wrap; /* 如果按鈕太多自動換行 */
    gap: 20px; /* 按鈕之間的間距 */
    padding: 20px; /* 內邊距 */
}

.mainbtn{
    width: 10vw;
    height: 10vw;
    border-radius: 10px;
    border: none;
    color: #6F8FF6;
    background-color: rgba(111, 143, 246, 0.4);
    padding-top: 8vw;
}
#detailsBtn{
    background-color: transparent;
    background-image: url(../html/assets/project_details.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#operationBtn{
    background-color: transparent;
    background-image: url(../html/assets/project_group.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#exhibitionBtn{
    background-color: transparent;
    background-image: url(../html/assets/project_exhibition.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#monitoringBtn{
    background-color: transparent;
    background-image: url(../html/assets/project_monitoring.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}
#statisticsBtn{
    background-color: transparent;
    background-image: url(../html/assets/project_statistics.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#backBtn{
    background-color: transparent;
    background-image: url(../html/assets/exhibition_return.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#resourcesBtn{
    background-color: transparent;
    background-image: url(../html/assets/exhibition_resources.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#productListBtn{
    background-color: transparent;
    background-image: url(../html/assets/exhibition_product.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#userListBtn{
    background-color: transparent;
    background-image: url(../html/assets/userlist.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#voucherListBtn{
    background-color: transparent;
    background-image: url(../html/assets/voucher.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}

#settingBtn{
    background-color: transparent;
    background-image: url(../html/assets/settings.png);
    background-position: center center;
    background-size: 100%;
    background-repeat: no-repeat;
}