label{
	color: #6F8FF6;
}

.btn-list {
	position: fixed;
	bottom: 50px;
    display: flex;
    left: 5vw;
    flex-direction: column; /* 讓子元素 (按鈕) 垂直排列 */
    gap: 20px; /* 按鈕之間的間距 */
    align-items: flex-start; /* 確保按鈕靠左對齊 (可選) */
}
.btn-list button{
	color: #6F8FF6;
	width: 50vw;
	height: 40px;
	font-size: 20px;
	text-align: left;
    border: none; /* 移除按鈕框線 */
    box-shadow: none; /* 移除按鈕陰影 */
    background-color: transparent; /* 如果你想讓按鈕背景也是透明的 */
    outline: none; /* 避免點擊時出現輪廓框 */
}

.maincontent{
	position: relative;
	padding-top: 10px;
	padding-left: 50px;
	padding-right: 50px;
	padding-bottom: 10px;
	width: 60vw;
	height: calc(100vh - 190px);
	left: 50vw;
	transform: translate(-50%, 0);
    align-items: center;
	background-color: #303030;
	border-radius: 20px;
}