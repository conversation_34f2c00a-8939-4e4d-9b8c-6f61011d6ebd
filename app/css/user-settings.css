label{
	color: #6F8FF6;
}
form {
	position: relative;
    font-size: 18px;
    font-family: Arial, sans-serif;
    margin: 20px;
    max-width: 500px;
    transform: translate(-50%, 0);
    left: 50%;
}

#edit-form{
	top: 200px;
}
.editbox{
    display: flex;
    gap: 20px;
}
.loginimg{
    position: relative;
    width: 150px;
    height: 150px;
}
.formbox{
    flex: 3;
}
.form-item {
    display: flex; /* 使用 Flexbox 排列 */
    align-items: center; /* 垂直方向置中 */
    margin-bottom: 20px; /* 每項之間的間距 */
}

.form-item label {
    width: 100px; /* 固定 label 的寬度，確保對齊 */
    text-align: left; /* 讓 label 文字靠右對齊 */
    margin-right: 10px; /* label 和 input 之間的間距 */
    color: #6F8FF6;
    margin-top: 0px;
}

.form-item input {
    flex: 1; /* 讓 input 填滿剩餘空間 */
    padding: 5px; /* 增加內邊距 */
    font-size: 16px; /* 設定字體大小 */
    border: 1px solid #151515; /* 邊框樣式 */
    border-radius: 5px; /* 圓角邊框 */
    background-color: #151515;
    color: #ffffff;
    margin-top: 0px;
    margin-bottom: 0px;
}

#submit {
    display: block; /* 让按钮成为块级元素，才会生效居中 */
    margin: 20px auto; /* 上下间距 20px，左右自动居中 */
    margin-top: 40px;
    padding: 10px 20px;
    font-size: 16px;
    background-color: #000000; /* 深蓝色背景 */
    color: #6F8FF6; /* 白色文字 */
    border-color: #6F8FF6;
    border-radius: 24px; /* 圆角按钮 */
    cursor: pointer; /* 鼠标悬浮变成手势 */
    width: 130px;
}

#submit:hover {
    background-color: #5AB0E6; /* 鼠标悬停时变浅 */
}