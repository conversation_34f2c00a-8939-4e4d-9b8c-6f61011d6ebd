body {
    position: relative; /* 為了讓固定定位的元素基於這個位置 */
    margin:0;
    padding: 0;
    background-color: black;
    color:white;
}

header{
    padding:2px;
    font-size: 48px;
    background-color: #202020;
    color:white;
}

header a{
    float:right;
    padding-left:20px;
    padding-right: 20px;
    text-decoration: none;
}

header > a:visited {
    color: #007BFF; /* 設定點擊後的顏色 */
}

header label{
    font-size: 40px;
    font-weight: bolder;
}

input{
    background-color: gray;
    color:white;
}

dialog{
    color:white;
    background-color: black;
    border: none;
    box-shadow: 0 2px 6px #ccc;
    z-index: 2;
}

.dialog-container{
    position: absolute;
    width:100%;
}

.list-container{
    display: flex;
}

ul{
    list-style-type:none;
    padding:0px;
    margin:0px;
    text-align: center;
}

li{
    justify-self: center;
}

nav {
    margin-bottom: 20px;
}
nav button {
    margin-right: 10px;
    padding: 10px;
}
thead{
    width: 100%;
}

th, td {
    padding: 12px;
    /*border: 1px solid #ccc;*/
    text-align: left;
}

th {
    /*background-color: #f2f2f2;*/
    cursor: pointer;
}

th.resizable {
    position: relative;
}

th.resizable div {
    position: absolute;
    top: 0;
    right: 0;
    width: 5px;
    height: 100%;
    cursor: col-resize;
}

.selected{
    background-color: gray;
}

select{
    font-size: 18px;
}

#content-section {
    height:70vh;
    padding: 20px;
}
#action-section {
    position: fixed; /* 固定定位 */
    bottom:20px;
    right:50%;
    transform: translate(50%,0);
    margin-top: 75px;
    padding: 10px;
    border: 2px solid #202c49; /* 專案操作區的邊框 */
    z-index: 1;
}
#action-section button {
    background-color: #202c49; /* 藍色背景 */
    color: #6F8FF6;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    margin-bottom: 5px; /* 按鈕之間的間距 */
}

.no-indent{
    list-style-type: none;
    padding-left:0px;
    margin-left:0px;
}
  
/* 滑過效果 */
#projects-table li:hover {
    background-color: #e0e0e0; /* 改變背景色 */
}

/* 選中的項目效果 */
#projects-table li.active {
    background-color: #d1eaff; /* 選中後背景色 */
    font-weight: bold;         /* 加粗字體 */
}

form {
    font-size: 18px;
    font-family: Arial, sans-serif;
    margin: 20px;
    max-width: 1200px;
}

form label {
    display: block;
    margin-top: 10px;
    font-weight: bold;
}

input[type="text"],
input[type="number"],
input[type="time"],
input[type="password"],
textarea{
    width: 100%;
    color:white;
    padding: 8px;
    margin-top: 5px;
    margin-bottom: 15px;
    font-size: 18px;
    border: 1px solid #444;
    border-radius: 4px;
    background-color: #444;
}

button[type="submit"] {
    padding: 10px 20px;
    font-size: 18px;
    background-color: #4CAF50;
    color: white;
    border-radius: 5px;
    cursor: pointer;
}

button[type="submit"]:hover {
    background-color: #45a049;
}

input:focus {
    border-color: #4CAF50;
    outline: none;
}

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

.left{
    float:left;
    width:49%;
}

.right{
    float:right;
    width: 49%;
}

@media (max-width: 800px) {
    input[type="number"],
    input[type="time"] {
        max-width: 100%;
    }
    .left{
        float:none;
        width:100%;
    }
    .right{
        float:none;
        width:100%;
    }
}

/* Popup container */
.popup {
    display: none;
}

.popup.show {
    position: absolute;
    display: block;
    min-width: 160px;
    max-width: 300px;
    top:50%;
    left: 50%;
    transform: translate(-50%, -50%);
    margin: 0 auto;
    padding: 20px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1;
}

.details-header{
    box-shadow: 0 1px 0 #ddd;
    margin-bottom: 10px;
}
#languageBtn{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}
#openUserDialog{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}
#projectsBtn{
    color: white;
    font-size: 20px;
    padding-top: 10px;
}