label{
	color: #6F8FF6;
}
.loginimg{
    position: relative;
    width: 150px;
    height: 150px;
}
.detailbox{
    display: flex;
    gap: 20px;
}
.databox{
    flex: 3;
}
.details{
	position: relative;
	top: 200px;
	font-size: 18px;
    font-family: Arial, sans-serif;
    margin: 20px;
    max-width: 400px;
    transform: translate(-50%, 0);
    left: 50%;
}
.details-item {
    display: flex; /* 使用 Flexbox 排列 */
    align-items: center; /* 垂直方向置中 */
    margin-bottom: 20px; /* 每項之間的間距 */
}
.details-name {
    width: 100px; /* 固定 label 的寬度，確保對齊 */
    text-align: left; /* 讓 label 文字靠右對齊 */
    margin-right: 10px; /* label 和 input 之間的間距 */
    color: #6F8FF6;
    margin-top: 0px;
}
.details-content{
    flex: 1; /* 讓 input 填滿剩餘空間 */
    padding: 5px; /* 增加內邊距 */
    font-size: 16px; /* 設定字體大小 */
    border-radius: 5px; /* 圓角邊框 */
    color: #ffffff;
    margin-top: 0px;
    margin-bottom: 0px;
}