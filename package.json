{"name": "iamm", "version": "1.0.0", "description": "```sql ## Database CREATE TABLE User(     account varchar(255) NOT NULL,     password varchar(255) NOT NULL,     name varchar(255),     phone int,     email varchar(255),     PRIMARY KEY(account) ); CREATE TABLE Projects(     id int NOT NULL auto_increment,     name varchar(255) NOT NULL,     time_start DATETIME,     time_end DATETIME,     PRIMARY KEY(id) );", "main": "index.js", "scripts": {"dev": "cd app && vite", "build": "cd app && vite build", "preview": "cd app && vite preview", "dev-game": "cd reference/game/MetaTagDisplayClient && node server.js", "docker:start": "./scripts/start-docker.sh", "docker:start-manual": "npm run docker:mysql && sleep 30 && npm run docker:init-db && npm run docker:app", "docker:mysql": "docker-compose -f docker-compose.mysql.yml up -d", "docker:app": "docker-compose -f docker-compose.app.yml up -d", "docker:monitoring": "docker-compose -f docker-compose.monitoring.yml up -d", "docker:stop": "./scripts/stop-docker.sh", "docker:stop-manual": "docker-compose -f docker-compose.app.yml down && docker-compose -f docker-compose.mysql.yml down && docker-compose -f docker-compose.monitoring.yml down", "docker:restart": "npm run docker:stop && npm run docker:start", "docker:logs": "docker-compose -f docker-compose.app.yml logs -f", "docker:logs-mysql": "docker-compose -f docker-compose.mysql.yml logs -f", "docker:dev": "concurrently \"npm run backend:dev\" \"npm run frontend:dev\"", "docker:init-db": "npm run prisma:generate && npm run prisma:migrate", "docker:clean": "docker-compose -f docker-compose.app.yml down -v && docker-compose -f docker-compose.mysql.yml down -v && docker system prune -f", "docker:rebuild": "./scripts/rebuild-docker.sh", "docker:restart-simple": "./scripts/restart-docker.sh", "backend:dev": "cd backend && npm install && npm run dev", "backend:build": "cd backend && npm run build", "backend:start": "cd backend && npm start", "frontend:dev": "cd app && npm install && npm run dev", "frontend:build": "cd app && npm run build", "prisma:generate": "npx prisma generate", "prisma:migrate": "npx prisma migrate dev --name init", "prisma:deploy": "npx prisma migrate deploy", "prisma:reset": "npx prisma migrate reset --force", "prisma:studio": "npx prisma studio", "prisma:seed": "npx prisma db seed", "pm2:start": "npm run pm2:stop && npm run pm2:iamm && sleep 5 && npm run pm2:game && npm run pm2:vendor", "pm2:stop": "pm2 delete all || true", "pm2:restart": "pm2 restart all", "pm2:logs": "pm2 logs", "pm2:status": "pm2 status", "dev-pm2": "NODE_ENV='DEV' && concurrently \"npm run pm2-iamm\" \"npm run pm2-game-core\" \" npm run pm2-game-database\" \"npm run pm2-vending-displayer\" \"npm run pm2-vending-controller\"\"npm run pm2-cors\"", "production-pm2": "NODE_ENV='PRODUCTION' && concurrently \"npm run pm2-iamm\" \"npm run pm2-game-core\" \" npm run pm2-game-database\" \"npm run pm2-vending-displayer\" \"npm run pm2-vending-controller\"\"npm run pm2-cors\"", "pm2-iamm": "pm2 start backend/src/index.ts --name iamm-backend", "pm2-vendor": "concurrently \"npm run pm2-vending-displayer\" \"npm run pm2-vending-controller\" \"npm run pm2-cors\"", "pm2-game": "concurrently \"npm run pm2-game-core\" \" npm run pm2-game-database\" \"npm run pm2-cors\"", "pm2-game-core": "cd reference/game/MetaTagDisplayClient && pm2 start server.js --name game-core", "pm2-game-database": "cd reference/game/MetaTagDatabase && pm2 start database.js --name game-database", "pm2-cors": "cd reference/game/Logger/src && pm2 start cors.js --name cors-logger", "pm2-vending-displayer": "cd reference/vending_machine/displayer && pm2 start server.js --name vending-displayer", "pm2-vending-controller": "cd reference/vending_machine/controller && pm2 start server.js --name vending-controller", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "kill": "npx kill-port 3001", "kill:all": "npx kill-port 3001 3061 3062 3306", "browserify-qrcode": "browserify app/src/utils/qr-code.mjs -p esmify > app/src/bundle.js", "browserify-qrcode-vending": "browserify app/src/utils/qr-code-vending.mjs -p esmify > app/src/vending.js", "migrate": "npx prisma migrate dev", "health:check": "curl -f http://localhost:3061/health || exit 1", "setup": "npm install && cd backend && npm install && cd ../app && npm install", "check:versions": "./scripts/check-versions.sh", "test:rebuild": "./scripts/test-rebuild.sh", "update:prisma": "npm install @prisma/client@^6.11.1 prisma@^6.11.1 && cd backend && npm install @prisma/client@^6.11.1 prisma@^6.11.1"}, "author": "<PERSON>", "prisma": {"schema": "prisma/schema.prisma"}, "type": "module", "jest": {"transform": {"^.+\\.jsx?$": "babel-jest"}, "testEnvironment": "jest-environment-jsdom", "setupFiles": ["./setupTests.js"]}, "dependencies": {"@aws-sdk/client-s3": "^3.675.0", "@aws-sdk/lib-storage": "^3.675.0", "@prisma/client": "^6.11.1", "aws-sdk": "^2.1691.0", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "concurrently": "^9.1.0", "cors": "^2.8.5", "crypto-js": "^4.2.0", "express": "^4.21.0", "express-http-proxy": "^2.1.1", "express-jwt": "^8.4.1", "http-proxy-middleware": "^3.0.3", "i18next": "^24.1.2", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "prom-client": "^15.1.3", "qrcode": "^1.5.4", "socket.io": "^4.8.0", "three-csg-ts": "^3.2.0", "uuid": "^10.0.0", "vite": "^6.1.1", "vite-express": "^0.20.0", "winston": "^3.17.0", "ws": "^8.18.0"}, "devDependencies": {"@babel/cli": "^7.25.9", "@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@testing-library/dom": "^10.4.0", "@types/bcrypt": "^5.0.2", "@types/body-parser": "^1.19.5", "@types/express": "^5.0.0", "@types/express-http-proxy": "^1.6.6", "@types/multer": "^1.4.12", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "@types/ws": "^8.5.12", "babel-jest": "^29.7.0", "browser-resolve": "^2.0.0", "dotenv": "^16.4.5", "esmify": "^2.1.1", "i": "^0.3.7", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^25.0.1", "node-ts": "^6.1.3", "nodemon": "^3.1.7", "npm": "^10.9.0", "prisma": "^6.11.1", "ts-node": "^10.9.2", "typescript": "^5.6.3", "whatwg-fetch": "^3.6.20"}, "nx": {}}